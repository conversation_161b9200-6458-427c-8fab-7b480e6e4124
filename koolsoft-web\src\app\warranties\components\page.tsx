'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Wrench, 
  Search, 
  Filter, 
  FileDown, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  Settings
} from 'lucide-react';
import Link from 'next/link';

/**
 * Component Tracking Page
 * 
 * This page displays and manages individual components and their warranty status.
 * It includes component-level warranty tracking and expiration alerts.
 */
export default function ComponentTrackingPage() {
  const [components, setComponents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [machineFilter, setMachineFilter] = useState('all');
  const [showAddDialog, setShowAddDialog] = useState(false);

  // Load warranty components from API
  useEffect(() => {
    const loadComponents = async () => {
      try {
        setIsLoading(true);

        const response = await fetch('/api/warranties/components', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch warranty components');
        }

        const data = await response.json();
        setComponents(data.components || []);
        setError(null);
      } catch (err) {
        console.error('Error loading components:', err);
        setError('Failed to load component data');
        setComponents([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadComponents();

    // Listen for add component event from layout
    const handleAddComponent = () => {
      setShowAddDialog(true);
    };

    window.addEventListener('addWarrantyComponent', handleAddComponent);
    return () => window.removeEventListener('addWarrantyComponent', handleAddComponent);
  }, []);

  const getWarrantyStatusBadge = (warrantyDate: string) => {
    const today = new Date();
    const warranty = new Date(warrantyDate);
    const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiry < 0) {
      return <Badge variant="destructive" className="flex items-center space-x-1">
        <AlertTriangle className="h-3 w-3" />
        <span>Expired</span>
      </Badge>;
    }

    if (daysUntilExpiry <= 30) {
      return <Badge variant="secondary" className="flex items-center space-x-1 bg-yellow-100 text-yellow-800">
        <Clock className="h-3 w-3" />
        <span>Expiring Soon</span>
      </Badge>;
    }

    return <Badge variant="secondary" className="flex items-center space-x-1 bg-green-100 text-green-800">
      <CheckCircle className="h-3 w-3" />
      <span>Active</span>
    </Badge>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const filteredComponents = components.filter(component => {
    const matchesSearch = searchTerm === '' || 
      component.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.machine.warranty.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.machine.product.name.toLowerCase().includes(searchTerm.toLowerCase());

    const warrantyDate = new Date(component.warrantyDate);
    const today = new Date();
    const isExpired = warrantyDate < today;
    const isExpiring = !isExpired && warrantyDate <= new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
    const isActive = !isExpired && !isExpiring;

    let matchesStatus = true;
    if (statusFilter === 'active') matchesStatus = isActive;
    else if (statusFilter === 'expiring') matchesStatus = isExpiring;
    else if (statusFilter === 'expired') matchesStatus = isExpired;
    
    return matchesSearch && matchesStatus;
  });

  const handleExport = async () => {
    try {
      const response = await fetch('/api/warranties/components/export?format=CSV', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export component data');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `warranty-components-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting component data:', error);
      setError('Failed to export component data');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Component Tracking</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Track individual components and their warranty status across all machines
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleExport}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="secondary" onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Component
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="search" className="text-black">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by component, machine, or customer..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-black">Warranty Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="expiring">Expiring Soon</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="machine" className="text-black">Machine</Label>
              <Select value={machineFilter} onValueChange={setMachineFilter}>
                <SelectTrigger id="machine">
                  <SelectValue placeholder="Select machine" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Machines</SelectItem>
                  {/* TODO: Populate with actual machines */}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-black">{error}</AlertDescription>
            </Alert>
          )}

          {/* Components Table */}
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-black">Component</TableHead>
                  <TableHead className="text-black">Machine</TableHead>
                  <TableHead className="text-black">Customer</TableHead>
                  <TableHead className="text-black">Product</TableHead>
                  <TableHead className="text-black">Warranty Date</TableHead>
                  <TableHead className="text-black">Status</TableHead>
                  <TableHead className="text-right text-black">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-28" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-6 w-16 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredComponents.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <Settings className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No components found</p>
                        <Button onClick={() => setShowAddDialog(true)}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add First Component
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredComponents.map((component) => (
                    <TableRow key={component.id}>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">#{component.componentNo}</div>
                          <div className="text-sm text-gray-500">SN: {component.serialNumber}</div>
                          <div className="text-sm text-gray-500">Section: {component.section}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">{component.machine?.serialNumber || 'Unknown Serial'}</div>
                          <div className="text-sm text-gray-500">{component.machine?.location || 'Unknown Location'}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">{component.machine?.warranty?.customer?.name || 'Unknown Customer'}</div>
                          <div className="text-sm text-gray-500">{component.machine?.warranty?.customer?.city || 'Unknown City'}</div>
                          <div className="text-sm text-gray-500">BSL: {component.machine?.warranty?.bslNo || 'N/A'}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">{component.machine?.product?.name || 'Unknown Product'}</div>
                          <div className="text-sm text-gray-500">{component.machine?.model?.name || 'Unknown Model'}</div>
                          <div className="text-sm text-gray-500">{component.machine?.brand?.name || 'Unknown Brand'}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span>{formatDate(component.warrantyDate)}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getWarrantyStatusBadge(component.warrantyDate)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/warranties/components/${component.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/warranties/components/${component.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination would go here */}
          {!isLoading && filteredComponents.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-gray-600">
                Showing {filteredComponents.length} of {components.length} components
              </p>
              {/* TODO: Add pagination controls */}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Component Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-black">Add New Component</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p className="text-black">Component form will be implemented here.</p>
            {/* TODO: Implement ComponentForm component */}
            <div className="flex justify-end space-x-2 mt-4">
              <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowAddDialog(false)}>
                Add Component
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

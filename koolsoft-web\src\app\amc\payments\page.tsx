'use client';

import React, { useState } from 'react';
import { PaymentList } from '@/components/payments/payment-list';
import { PaymentForm } from '@/components/payments/payment-form';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Payment } from '@/lib/hooks/usePayments';

export default function PaymentsPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  const handleCreatePayment = () => {
    setIsCreateDialogOpen(true);
  };

  const handleEditPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsEditDialogOpen(true);
  };

  const handleDeletePayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
  };

  const handleEditSuccess = () => {
    setIsEditDialogOpen(false);
    setSelectedPayment(null);
  };

  const handleDeleteConfirm = () => {
    // The deletion will be handled by the PaymentList component
    setIsDeleteDialogOpen(false);
    setSelectedPayment(null);
  };

  const handleDialogClose = () => {
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setSelectedPayment(null);
  };

  return (
    <div className="space-y-6">
        {/* Payment List */}
        <PaymentList
          showContractInfo={true}
          onCreatePayment={handleCreatePayment}
          onEditPayment={handleEditPayment}
          onDeletePayment={handleDeletePayment}
        />

        {/* Create Payment Dialog */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-black">Create New Payment</DialogTitle>
            </DialogHeader>
            <PaymentForm
              onSuccess={handleCreateSuccess}
              onCancel={handleDialogClose}
            />
          </DialogContent>
        </Dialog>

        {/* Edit Payment Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-black">Edit Payment</DialogTitle>
            </DialogHeader>
            {selectedPayment && (
              <PaymentForm
                payment={selectedPayment}
                onSuccess={handleEditSuccess}
                onCancel={handleDialogClose}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="text-black">Delete Payment</AlertDialogTitle>
              <AlertDialogDescription className="text-black">
                Are you sure you want to delete this payment? This action cannot be undone.
                {selectedPayment && (
                  <div className="mt-2 p-3 bg-gray-50 rounded-md">
                    <p className="font-medium">
                      Payment: ₹{selectedPayment.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                    </p>
                    {selectedPayment.receiptNo && (
                      <p className="text-sm text-gray-600">
                        Receipt: {selectedPayment.receiptNo}
                      </p>
                    )}
                  </div>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteConfirm}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete Payment
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </div>
  );
}
